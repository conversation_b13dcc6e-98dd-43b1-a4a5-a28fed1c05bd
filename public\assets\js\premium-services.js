/**
 * Optimized Services Animation System
 * Subtle and performance-focused animations for the services section
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize optimized services animations
    initOptimizedServicesAnimations();

    function initOptimizedServicesAnimations() {
        const servicesSection = document.getElementById('services');
        if (!servicesSection) return;

        // Enhanced entrance animations for asymmetrical layout
        const serviceCards = servicesSection.querySelectorAll('[style*="animation-delay"]');
        const sectionHeader = servicesSection.querySelector('.text-center');

        // Intersection Observer for sophisticated entrance animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const entranceObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.remove('opacity-0', 'translate-y-8');
                    entry.target.classList.add('opacity-100', 'translate-y-0');
                    entranceObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe section header first
        if (sectionHeader) {
            entranceObserver.observe(sectionHeader);
        }

        // Observe all service cards with staggered timing
        serviceCards.forEach((card, index) => {
            entranceObserver.observe(card);
        });

        // Premium hover interactions for asymmetrical layout
        initPremiumHoverEffects();

        // Initialize accessibility enhancements
        initAccessibilityFeatures();
    }
    
    function initPremiumHoverEffects() {
        const serviceCards = document.querySelectorAll('#services .group');

        serviceCards.forEach((card, index) => {
            // Determine card type based on layout
            const isHero = card.querySelector('[class*="w-20 h-20"]');
            const isFeatured = !isHero && index < 2;

            // Premium hover effects with hierarchy awareness
            card.addEventListener('mouseenter', () => {
                // Enhanced icon scaling based on card hierarchy
                const iconContainer = card.querySelector('[class*="w-16 h-16"], [class*="w-20 h-20"]');
                if (iconContainer) {
                    const scaleValue = isHero ? '1.05' : '1.03';
                    iconContainer.style.transform = `scale(${scaleValue})`;
                    iconContainer.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                }

                // Subtle background glow enhancement
                const gradientOverlay = card.querySelector('[class*="bg-gradient-to-br"]');
                if (gradientOverlay && isHero) {
                    gradientOverlay.style.filter = 'brightness(1.05)';
                    gradientOverlay.style.transition = 'filter 0.3s ease';
                }
            });

            card.addEventListener('mouseleave', () => {
                // Reset all hover effects
                const iconContainer = card.querySelector('[class*="w-16 h-16"], [class*="w-20 h-20"]');
                if (iconContainer) {
                    iconContainer.style.transform = 'scale(1)';
                }

                const gradientOverlay = card.querySelector('[class*="bg-gradient-to-br"]');
                if (gradientOverlay) {
                    gradientOverlay.style.filter = 'brightness(1)';
                }
            });

            // Add premium focus handling for accessibility
            card.addEventListener('focus', () => {
                card.style.outline = '2px solid rgba(59, 130, 246, 0.5)';
                card.style.outlineOffset = '2px';
            });

            card.addEventListener('blur', () => {
                card.style.outline = 'none';
            });
        });
    }

    function initAccessibilityFeatures() {
        const serviceCards = document.querySelectorAll('#services [role="button"]');

        serviceCards.forEach((card, index) => {
            // Enhanced keyboard navigation
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // Trigger the CTA button click
                    const ctaButton = card.querySelector('a[href="#contact"]');
                    if (ctaButton) {
                        ctaButton.click();
                    }
                }

                // Arrow key navigation between service cards
                if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    e.preventDefault();
                    const nextCard = serviceCards[index + 1] || serviceCards[0];
                    nextCard.focus();
                }

                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prevCard = serviceCards[index - 1] || serviceCards[serviceCards.length - 1];
                    prevCard.focus();
                }
            });

            // Announce service details for screen readers
            card.addEventListener('focus', () => {
                const title = card.querySelector('h3').textContent;
                const description = card.querySelector('p').textContent;

                // Create live region announcement
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = `Service: ${title}. ${description}. Press Enter to consult.`;

                document.body.appendChild(announcement);

                // Remove announcement after screen reader has time to read it
                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 3000);
            });
        });

        // Add skip link for services section
        const skipLink = document.createElement('a');
        skipLink.href = '#services';
        skipLink.textContent = 'Skip to services section';
        skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-500 text-white px-4 py-2 rounded z-50';
        skipLink.addEventListener('focus', () => {
            skipLink.classList.remove('sr-only');
        });
        skipLink.addEventListener('blur', () => {
            skipLink.classList.add('sr-only');
        });

        document.body.insertBefore(skipLink, document.body.firstChild);
    }

});

// Premium CSS enhancements for sophisticated services layout
const style = document.createElement('style');
style.textContent = `
    /* Premium Services Section Enhancements */
    #services .group {
        position: relative;
        isolation: isolate;
    }

    /* Sophisticated backdrop blur enhancement */
    #services .group::before {
        content: '';
        position: absolute;
        inset: 0;
        background: inherit;
        border-radius: inherit;
        filter: blur(0.5px);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    #services .group:hover::before {
        opacity: 0.1;
    }

    /* Premium focus ring for accessibility */
    #services .group:focus-visible {
        outline: 2px solid rgb(59 130 246 / 0.6);
        outline-offset: 2px;
        border-radius: 1.5rem;
    }

    /* Enhanced gradient animations */
    #services .group [class*="bg-gradient-to-br"] {
        background-size: 200% 200%;
        animation: gradientShift 8s ease-in-out infinite;
    }

    @keyframes gradientShift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* Respect user motion preferences */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        #services .group [class*="bg-gradient-to-br"] {
            animation: none !important;
        }
    }

    /* Premium responsive enhancements */
    @media (max-width: 1023px) {
        #services .group {
            min-height: 320px;
        }

        /* Tablet layout optimizations */
        #services .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-12 {
            gap: 1.5rem;
        }
    }

    @media (max-width: 767px) {
        #services .group {
            min-height: 280px;
        }

        /* Mobile layout optimizations */
        #services .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* Ensure proper spacing on mobile */
        #services .grid {
            gap: 1rem;
        }

        /* Mobile-first typography adjustments */
        #services h3 {
            font-size: 1.25rem !important;
            line-height: 1.4;
        }

        #services p {
            font-size: 0.875rem !important;
            line-height: 1.5;
        }
    }

    @media (max-width: 480px) {
        #services .group {
            min-height: 260px;
            border-radius: 1.5rem;
        }

        /* Extra small mobile optimizations */
        #services .relative.z-10 {
            padding: 1.25rem !important;
        }
    }

    /* Enhanced shadow depth for premium feel */
    #services .group:hover {
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    /* Accessibility enhancements */
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .focus\\:not-sr-only:focus {
        position: static;
        width: auto;
        height: auto;
        padding: 0.5rem 1rem;
        margin: 0;
        overflow: visible;
        clip: auto;
        white-space: normal;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        #services .group {
            border-width: 2px;
            border-color: currentColor;
        }

        #services .group:hover,
        #services .group:focus {
            border-color: #ffffff;
            background-color: rgba(0, 0, 0, 0.9);
        }
    }
`;
document.head.appendChild(style);
